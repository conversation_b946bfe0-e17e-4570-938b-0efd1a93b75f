-- Step 1: Drop the old function if it exists
DROP FUNCTION IF EXISTS sumAmountByDateRangeInstructionId(UUID, UUID, <PERSON>IMESTA<PERSON>, <PERSON>IMESTA<PERSON>, INTEGER);

-- Step 2: Create the new sp
CREATE OR REPLACE FUNCTION sumAmountByDateRangeInstructionId(
    p_account_ref_id UUID,
    p_profile_ref_id UUID,
    p_start_date TIMESTAMP, -- This is the snapshot's effectiveToDateTime
    p_end_date TIMESTAMP,
    p_instruction_id INT
)
    RETURNS NUMERIC AS $$
DECLARE
    new_transactions_total NUMERIC;
    finalized_transactions_total NUMERIC;
    total_change NUMERIC;
BEGIN
    -- 1. Sum of NEW transactions (based on effective_date_time) since the snapshot
    -- This captures transactions that are entirely new.
    SELECT COALESCE(SUM(amount), 0)
    INTO new_transactions_total
    FROM transactions
    WHERE account_ref_id = p_account_ref_id
      AND profile_ref_id = p_profile_ref_id
      AND status IN ('POSTED', 'PENDING', 'REVERSED', 'INIT_PENDING')
      AND effective_date_time >= p_start_date
      AND effective_date_time <= p_end_date
      AND instruction_id <> p_instruction_id;

    -- 2. Sum of OLD transactions that were FINALIZED since the snapshot
    -- This captures state changes like PENDING -> POSTED or PENDING -> ROLLBACKED.
    SELECT COALESCE(SUM(amount), 0)
    INTO finalized_transactions_total
    FROM transactions
    WHERE account_ref_id = p_account_ref_id
      AND profile_ref_id = p_profile_ref_id
      -- Capture all transactions that reached a final state after the snapshot
      AND status IN ('POSTED', 'REVERSED', 'ROLLBACKED', 'ROLLBACKED_SYSTEM')
      AND finalization_date_time >= p_start_date
      AND finalization_date_time <= p_end_date
      AND effective_date_time < p_start_date
      AND effective_date_time > (p_start_date - INTERVAL '33 days')
      AND instruction_id <> p_instruction_id; -- Exclude the current instruction

    -- 3. Calculate total change.
    total_change := new_transactions_total - finalized_transactions_total;

    RETURN total_change;
END;
$$ LANGUAGE plpgsql;


-- Composite index for the NEW transactions query
CREATE INDEX CONCURRENTLY idx_transactions_new_composite
    ON transactions (
                     account_ref_id,
                     profile_ref_id,
                     effective_date_time,
                     status,
                     instruction_id
        )
    INCLUDE (amount)
    WHERE status IN ('POSTED', 'PENDING', 'INIT_PENDING', 'REVERSED');

-- Composite index for UPDATED transactions query
CREATE INDEX CONCURRENTLY idx_transactions_updated_composite
    ON transactions (
                     account_ref_id,
                     profile_ref_id,
                     finalization_date_time,
                     status,
                     effective_date_time,
                     instruction_id
        )
    INCLUDE (amount)
    WHERE status IN ('POSTED', 'REVERSED', 'ROLLBACKED', 'ROLLBACKED_SYSTEM');