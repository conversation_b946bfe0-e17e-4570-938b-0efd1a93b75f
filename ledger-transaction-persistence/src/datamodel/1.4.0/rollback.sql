DROP FUNCTION IF EXISTS sumAmountByDateRangeInstructionId(U<PERSON><PERSON>, UUI<PERSON>, <PERSON><PERSON>ESTA<PERSON>, TIMESTA<PERSON>, INTEGER);
CREATE OR REPLACE FUNCTION sumAmountByDateRangeInstructionId(
    p_accountRefId UUID,
    p_profileRefId UUID,
    p_effectiveDateTimeStart TIMESTAMP,
    p_effectiveDateTimeUntil TIMESTAMP,
    p_instructionId INTEGER
) RETURNS NUMERIC AS $$
DECLARE
    v_sum NUMERIC;
BEGIN
    SELECT sum(amount) INTO v_sum
    FROM transactions
    WHERE
        account_ref_id = p_accountRefId AND
        profile_ref_id = p_profileRefId AND
        status IN ('POSTED','PENDING','INIT_PENDING','REVERSED') AND
        (effective_date_time BETWEEN p_effectiveDateTimeStart AND p_effectiveDateTimeUntil) AND
        instruction_id <= p_instructionId;
    RETURN v_sum;
END; $$ LANGUAGE plpgsql parallel safe;

DROP INDEX IF EXISTS idx_transactions_new_composite;
DROP INDEX IF EXISTS idx_transactions_updated_composite;