-- ###############
-- # ENVIRONMENT #
-- ###############
-- # Based on env, set search path accordingly (uncomment one of the below)

-- # LOCAL
-- CREATE SCHEMA IF NOT EXISTS ledger_transaction
-- SET search_path to ledger_transaction;

-- # DEV 
-- SET search_path to transaction_dev;

-- # QAS
--  SET search_path to transaction_qa;


-- ######
-- TABLES
-- ######
DROP TABLE IF EXISTS transactions;
DROP TABLE IF EXISTS instructions;
DROP TABLE IF EXISTS balance;
DROP TABLE IF EXISTS transactions_metadata;

-- INDEXES for INSTRUCTIONS table
DROP INDEX IF EXISTS ix_instructions_profile_account_instruction_ref_id;
DROP INDEX IF EXISTS ix_instructions_status_created_date_time;
DROP INDEX IF EXISTS idx_instructions_rail_partial_internal;

-- INDEXES for TRANSACTIONS table
DROP INDEX IF EXISTS ix_transaction_effective_date_time;
DROP INDEX IF EXISTS ix_transaction_profile_account_instruction;
DROP INDEX IF EXISTS ix_transaction_profile_account_instruction_transaction;
DROP INDEX IF EXISTS ix_transaction_related_id;
DROP INDEX IF EXISTS idx_transactions_status;
DROP INDEX IF EXISTS ix_transactions_instruction_id;
DROP INDEX IF EXISTS idx_transactions_acceptance_date_time;
DROP INDEX IF EXISTS idx_transactions_new_composite;
DROP INDEX IF EXISTS idx_transactions_updated_composite;

-- INDEXES for BALANCE table
DROP INDEX IF EXISTS ix_balance_profile_account;

-- #####
-- TYPES
-- #####
DROP TYPE IF EXISTS instruction_status_type;
DROP TYPE IF EXISTS transaction_status_type;
DROP TYPE IF EXISTS transaction_hold_type;

-- drop the function
DROP FUNCTION IF EXISTS sumAmountByDateRangeInstructionId(UUID, UUID, TIMESTAMP, TIMESTAMP, INTEGER);

