package com.peoplestrust.transaction.persistence.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

import io.hypersistence.utils.hibernate.type.basic.PostgreSQLEnumType;
import jakarta.persistence.*;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;

@Data
@Entity
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Table(name = "transactions")
public class TransactionEntity extends DomainEntityTimeStamps {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id", unique = true, nullable = false)
  private Integer id;

  @NotNull
  @Column(name = "profile_ref_id",columnDefinition = "uuid")
  private UUID profileRefId;

  @NotNull
  @Column(name = "account_ref_id",columnDefinition = "uuid")
  private UUID accountRefId;

  @Column(name = "related_id")
  private Integer relatedId;

  @Column(name = "network_payment_ref_id")
  private String networkPaymentRefId;

  @Column(name = "transaction_ref_id")
  private String transactionRefId;

  @Enumerated(EnumType.STRING)
  @Column(name = "payment_category")
  private PaymentCategoryType paymentCategory;

  @Enumerated(EnumType.STRING)
  @Column(name = "transaction_flow")
  private TransactionFlowType transactionFlow;

  @Enumerated(EnumType.STRING)
  @Column(name = "transaction_hold",columnDefinition = "transactions.transaction_hold_type")
  @Type(PostgreSQLEnumType.class)
  private TransactionHoldType transactionHold;

  @Enumerated(EnumType.STRING)
  @Column(name = "status",columnDefinition = "transactions.transaction_status_type")
  @Type(PostgreSQLEnumType.class)
  private TransactionStatus status;

  @Column(name = "amount")
  @Digits(integer = 13, fraction = 2)
  private BigDecimal amount;

  @Enumerated(EnumType.STRING)
  @Column(name = "monetary_unit")
  private MonetaryUnit monetaryUnit;

  @Column(name = "acceptance_date_time")
  private LocalDateTime acceptanceDateTime;

  @Column(name = "due_date_time")
  private LocalDateTime dueDateTime;

  @Column(name = "effective_date_time")
  private LocalDateTime effectiveDateTime;

  @ManyToOne(optional = false, fetch = FetchType.EAGER, cascade = CascadeType.REMOVE)
  @JoinColumn(name = "instruction_id")
  private InstructionEntity instruction;

  @Column(name = "finalization_date_time")
  private LocalDateTime finalizationDateTime;
}

