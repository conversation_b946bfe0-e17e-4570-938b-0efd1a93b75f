package com.peoplestrust.account.external.api.v1.mapper;

import com.peoplestrust.account.client.domain.model.RetrieveLedgerAccountBalanceByClientResponse;
import com.peoplestrust.account.external.api.v1.model.RedisBalance;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface ResponseMapper {

  RetrieveLedgerAccountBalanceByClientResponse fromRedisBalanceToRetrieveLedgerAccountBalanceByClientResponse(RedisBalance redisBalance);
}
