package com.peoplestrust.transaction.scheduler.service.impl;

import com.peoplestrust.transaction.persistence.entity.InstructionEntity;
import com.peoplestrust.transaction.persistence.repository.read.ReadInstructionRepository;
import com.peoplestrust.transaction.scheduler.service.PartitionedTransactionRollBackJobRunner;
import com.peoplestrust.transaction.scheduler.service.TransactionServiceAdapter;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PartitionedTransactionRollBackJobRunnerImpl implements PartitionedTransactionRollBackJobRunner {

  @Value("${scheduler.rollback.expiration.days}")
  private long rollbackExpirationDays;

  @Value("${scheduler.rollback.instructions.fetch.limit}")
  private int fetchLimit;

  @Autowired
  TransactionServiceAdapter transactionServiceAdapter;

  @Autowired
  ReadInstructionRepository readInstructionRepository;

  @Override
  public void runRollBackJob() {
    List<InstructionEntity> instructionsForRollBack;
    do {
      instructionsForRollBack = fetchExpiredTransactions();
      if (instructionsForRollBack.isEmpty()) {
        log.info("No instructions found for rollback, terminating the job.");
        return;
      }

      log.info("Found {} instructions eligible for rollback", instructionsForRollBack.size());
      try {
        transactionServiceAdapter.rollBackTransactions(instructionsForRollBack);
      } catch (Exception ex) {
        log.error("Rollback failed, aborting current run", ex);
        return;
      }
    } while (true);
  }

  List<InstructionEntity> fetchExpiredTransactions() {
    LocalDateTime rollbackStartTime = LocalDateTime.now()
        .toLocalDate()
        .atStartOfDay()
        .minusDays(rollbackExpirationDays);

    log.info("RollBack check rollbackStartTime : {} fetchLimit:{}", rollbackStartTime, fetchLimit);
    return readInstructionRepository.findRollbackInstructions(
        rollbackStartTime,
        PageRequest.of(0, fetchLimit));
  }
}
