package com.peoplestrust.transaction.api.v1.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.peoplestrust.transaction.persistence.entity.PaymentCategoryType;
import com.peoplestrust.transaction.persistence.entity.TransactionFlowType;
import com.peoplestrust.transaction.persistence.entity.TransactionHoldType;
import com.peoplestrust.transaction.persistence.entity.TransactionStatus;
import com.peoplestrust.util.api.common.util.LocalDateTimeDeserializer;
import com.peoplestrust.util.api.common.util.LocalDateTimeSerializer;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Data
@Setter
@Getter
@Builder
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor

/**
 * Transaction DTO.
 */
public class Transaction {

  private Integer id;
  private String profileRefId;
  private String accountRefId;
  private Integer relatedId;
  private String networkPaymentRefId;
  private String transactionRefId;
  private PaymentCategoryType paymentCategory;
  private TransactionFlowType transactionFlow;
  private TransactionHoldType transactionHold;
  private TransactionStatus status;
  private BigDecimal amount;
  private String monetaryUnit;
  private OffsetDateTime acceptanceDateTime;
  private OffsetDateTime dueDateTime;
  private OffsetDateTime effectiveDateTime;
  private OffsetDateTime finalizationDateTime;

  @JsonDeserialize(using = LocalDateTimeDeserializer.class)
  @JsonSerialize(using = LocalDateTimeSerializer.class)
  private LocalDateTime createdDateTime;

  @JsonDeserialize(using = LocalDateTimeDeserializer.class)
  @JsonSerialize(using = LocalDateTimeSerializer.class)
  private LocalDateTime updatedDateTime;
}

