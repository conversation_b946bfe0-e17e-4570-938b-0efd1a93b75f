package com.peoplestrust.transaction.api.v1.service;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.peoplestrust.transaction.api.v1.mapper.TransactionMapper;
import com.peoplestrust.transaction.api.v1.model.Instruction;
import com.peoplestrust.transaction.persistence.entity.*;
import com.peoplestrust.transaction.persistence.repository.write.InstructionRepository;
import com.peoplestrust.util.api.common.exception.InvalidStatusTransitionException;
import java.time.OffsetDateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.List;
import java.util.UUID;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)   // keep strict stubbing but allow lenient mocks
class CommitTransactionServiceTest {

  private static final String INSTR_ID       = UUID.randomUUID().toString();
  private static final String PROFILE_ID     = UUID.randomUUID().toString();
  private static final String ACCOUNT_ID     = UUID.randomUUID().toString();
  private static final String INTERACTION_ID = UUID.randomUUID().toString();

  @Mock(lenient = true) ValidationService     validationService;   // lenient: same stub for every test
  @Mock(lenient = true) TransactionMapper     mapper;              // unused in failure case
  @Mock               InstructionRepository   instructionRepo;

  @InjectMocks
  TransactionServiceImpl service;   //  <- your service implementation

  private InstructionEntity instruction;   // reused fixture
  private TransactionEntity tx;

  /* ------------------------------------------------------------------ */
  @BeforeEach
  void setUp() throws Exception {

    /* ---- build a reusable instruction with one transaction -------- */
    tx = new TransactionEntity();
    tx.setTransactionRefId(UUID.randomUUID().toString());

    instruction = new InstructionEntity();
    instruction.setInstructionRefId(INSTR_ID);
    instruction.setProfileRefId(UUID.fromString(PROFILE_ID));
    instruction.setAccountRefId(UUID.fromString(ACCOUNT_ID));
    instruction.setTransactions(List.of(tx));

    /* ---- common stubs -------------------------------------------- */
    /* validateProfileAndAccount returns *something* (not void) */
    lenient().when(validationService.validateProfileAndAccount(any(), any(), any()))
        .thenReturn(Boolean.TRUE);   // return type doesn’t matter; value is ignored

    lenient().when(mapper.fromInstructionEntityToInstruction(any()))
        .thenReturn(new Instruction());

    when(instructionRepo.save(any()))
        .thenAnswer(inv -> inv.getArgument(0));  // echo-back
  }

  /* ------------------------------------------------------------------
   * 1) PENDING → POSTED → success
   * ------------------------------------------------------------------ */
  @Test
  void commit_pending_transaction_updates_status() throws Exception {

    tx.setStatus(TransactionStatus.PENDING);
    instruction.setStatus(InstructionStatus.PENDING);

    when(instructionRepo.findByProfileRefIdAndAccountRefIdAndInstructionRefId(
        any(), any(), eq(INSTR_ID)))
        .thenReturn(instruction);

    service.commitTransaction(INSTR_ID, PROFILE_ID, ACCOUNT_ID, INTERACTION_ID);

    assertThat(tx.getStatus()).isEqualTo(TransactionStatus.POSTED);
    assertThat(instruction.getStatus()).isEqualTo(InstructionStatus.POSTED);
    verify(instructionRepo).save(instruction);
  }

  /* ------------------------------------------------------------------
   * 2) already POSTED → idempotent success
   * ------------------------------------------------------------------ */
  @Test
  void commit_already_posted_transaction_is_idempotent() throws Exception {

    tx.setStatus(TransactionStatus.POSTED);
    instruction.setStatus(InstructionStatus.POSTED);

    when(instructionRepo.findByProfileRefIdAndAccountRefIdAndInstructionRefId(
        any(), any(), eq(INSTR_ID)))
        .thenReturn(instruction);

    service.commitTransaction(INSTR_ID, PROFILE_ID, ACCOUNT_ID, INTERACTION_ID);

    assertThat(tx.getStatus()).isEqualTo(TransactionStatus.POSTED);  // unchanged
    verify(instructionRepo).save(instruction);
  }

  /* ————————————————————————————————————————————
      3) ANY status other than PENDING/POSTED → error
      ———————————————————————————————————————————— */
  @ParameterizedTest
  @EnumSource(
      value = TransactionStatus.class,
      names = { "PENDING", "POSTED" },          // exclude the legal statuses
      mode  = EnumSource.Mode.EXCLUDE)
  @DisplayName("commit with illegal status throws InvalidStatusTransitionException")
  void commit_with_illegal_status_throws(TransactionStatus illegalStatus) {

    tx.setStatus(illegalStatus);
    instruction.setStatus(InstructionStatus.PENDING);   // instruction passes its own check

    when(instructionRepo.findByProfileRefIdAndAccountRefIdAndInstructionRefId(
        any(), any(), eq(INSTR_ID)))
        .thenReturn(instruction);

    /* ── capture the exception ─────────────────────────────────────── */
    Throwable thrown = catchThrowable(() ->
        service.commitTransaction(INSTR_ID, PROFILE_ID, ACCOUNT_ID, INTERACTION_ID));

    /* ── console log (or use a logger) ─────────────────────────────── */
    System.out.println("▶ Illegal status [" + illegalStatus + "] → " + thrown.getMessage());

    /* ── verify the exact message format ───────────────────────────── */
    String expectedMessage = String.format("Transaction Status is %s", illegalStatus);

    assertThat(thrown)
        .isInstanceOf(InvalidStatusTransitionException.class)
        .hasMessage(expectedMessage);               // exact match

    /* repo.save must never be invoked on failure */
    verify(instructionRepo, never()).save(any());
  }
  @Test
  void commit_pending_transaction_updates_status_and_sets_finalization_time() throws Exception {
    tx.setStatus(TransactionStatus.PENDING);
    instruction.setStatus(InstructionStatus.PENDING);
    tx.setFinalizationDateTime(null); // Ensure it starts as null

    when(instructionRepo.findByProfileRefIdAndAccountRefIdAndInstructionRefId(
        any(), any(), eq(INSTR_ID)))
        .thenReturn(instruction);

    service.commitTransaction(INSTR_ID, PROFILE_ID, ACCOUNT_ID, INTERACTION_ID);

    assertThat(tx.getStatus()).isEqualTo(TransactionStatus.POSTED);
    assertThat(tx.getFinalizationDateTime()).isNotNull(); // finalizationDateTime should be set
    assertThat(instruction.getStatus()).isEqualTo(InstructionStatus.POSTED);
    verify(instructionRepo).save(instruction);
  }
  @Test
  void commit_already_posted_transaction_does_not_change_finalization_time() throws Exception {
    OffsetDateTime originalFinalizationTime = OffsetDateTime.now().minusDays(1);
    tx.setStatus(TransactionStatus.POSTED);
    tx.setFinalizationDateTime(originalFinalizationTime.toLocalDateTime()); // Convert to LocalDateTime
    instruction.setStatus(InstructionStatus.POSTED);

    when(instructionRepo.findByProfileRefIdAndAccountRefIdAndInstructionRefId(
        any(), any(), eq(INSTR_ID)))
        .thenReturn(instruction);

    service.commitTransaction(INSTR_ID, PROFILE_ID, ACCOUNT_ID, INTERACTION_ID);

    // Ensure status remains the same
    assertThat(tx.getStatus()).isEqualTo(TransactionStatus.POSTED);

    // finalizationDateTime should NOT be updated
    assertThat(tx.getFinalizationDateTime()).isEqualTo(originalFinalizationTime.toLocalDateTime());

    verify(instructionRepo).save(instruction);
  }
}
