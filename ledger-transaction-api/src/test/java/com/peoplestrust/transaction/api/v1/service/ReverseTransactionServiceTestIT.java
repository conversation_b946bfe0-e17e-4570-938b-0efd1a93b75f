package com.peoplestrust.transaction.api.v1.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.peoplestrust.scheduler.persistence.repository.write.BalanceRepository;
import com.peoplestrust.transaction.api.v1.TransactionApplication;
import com.peoplestrust.transaction.api.v1.common.TestUtil;
import com.peoplestrust.transaction.api.v1.mapper.TransactionMapper;
import com.peoplestrust.transaction.api.v1.model.Account;
import com.peoplestrust.transaction.api.v1.model.Instruction;
import com.peoplestrust.transaction.api.v1.model.Transaction;
import com.peoplestrust.transaction.persistence.entity.InstructionEntity;
import com.peoplestrust.transaction.persistence.entity.MonetaryUnit;
import com.peoplestrust.transaction.persistence.entity.PaymentCategoryType;
import com.peoplestrust.transaction.persistence.entity.PaymentRailType;
import com.peoplestrust.transaction.persistence.entity.TransactionEntity;
import com.peoplestrust.transaction.persistence.entity.TransactionFlowType;
import com.peoplestrust.transaction.persistence.entity.TransactionHoldType;
import com.peoplestrust.transaction.persistence.entity.TransactionStatus;
import com.peoplestrust.transaction.persistence.repository.write.InstructionRepository;
import com.peoplestrust.transaction.persistence.repository.write.TransactionRepository;
import com.peoplestrust.util.api.common.util.DateUtils;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.utility.RandomString;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@SpringBootTest
@ActiveProfiles("test")
@ContextConfiguration(classes = TransactionApplication.class)
@ExtendWith(SpringExtension.class)
@Slf4j
public class ReverseTransactionServiceTestIT {


  private static String profileRefId = UUID.randomUUID().toString();
  private static String transactionRefId = UUID.randomUUID().toString();
  String accountRefId = UUID.randomUUID().toString();
  String interactionId = UUID.randomUUID().toString();
  @Autowired
  private TransactionRepository transactionRepository;
  @Autowired
  private TransactionServiceImpl transactionService;
  @Autowired
  private InstructionRepository instructionRepository;
  @Autowired
  BalanceRepository balanceRepository;
  @MockBean
  private ValidationService validationService;

  @Test
  public void reverseTransaction() throws Exception {

    Account account = TestUtil.createAccount(accountRefId, profileRefId);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    when(validationService.isProfileAvailable(any(), any())).thenReturn(true);
    when(validationService.validateProfileAndAccount(any(), any(), any())).thenReturn(true);
    Instruction instruction = createInstruction();

    // Transaction::Initiate
    Instruction savedInstruction = transactionService.initiateInstruction(instruction, profileRefId, accountRefId, interactionId);

    // Transaction::Submit
    Instruction postedInstruction = transactionService.commitTransaction(instruction.getInstructionRefId(), profileRefId, accountRefId, interactionId);

    // Transaction::Reverse (specific transaction)
    transactionService.reverseTransaction(postedInstruction.getInstructionRefId(), transactionRefId, profileRefId, accountRefId, interactionId);
    Transaction transaction = transactionService.getTransaction(postedInstruction.getInstructionRefId(), transactionRefId, profileRefId, accountRefId,
        interactionId);
    assertNotNull(transaction);
    assertEquals(transaction.getStatus(), TransactionStatus.REVERSED);

  }


  @AfterEach
  public void doCleanUpAfterTest() {
    log.trace("cleanup - start");
    instructionRepository.findByProfileRefIdAndAccountRefId(UUID.fromString(profileRefId), UUID.fromString(accountRefId)).stream().
        forEach(e -> {
          for (TransactionEntity t : e.getTransactions()) {
            if (t.getRelatedId() != null) {
              transactionRepository.delete(t);
            }
          }
          instructionRepository.delete(e);
        });
    log.trace("clean up - end");
  }


  private Instruction createInstruction() {
    String word = "TEST_INST" + RandomString.make(7);
    List<Transaction> transactions = createTransactions();

    Instruction instruction = Instruction.builder().instructionRefId(word).accountRefId(accountRefId).paymentRail(PaymentRailType.EFT)
        .profileRefId(profileRefId).transactions(transactions).createdDateTime(DateUtils.offset())
        .updatedDateTime(DateUtils.offset()).build();
    instruction.setTransactions(transactions);
    return instruction;
  }

  private List<Transaction> createTransactions() {
    List<Transaction> list = new ArrayList<>();

    Transaction t1 = Transaction.builder()
        .transactionRefId(transactionRefId)
        .paymentCategory(PaymentCategoryType.DEBIT_PULL)
        .amount(new BigDecimal(100))
        .transactionFlow(TransactionFlowType.CREDIT)
        .monetaryUnit(String.valueOf(MonetaryUnit.CAD))
        .acceptanceDateTime(DateUtils.offsetDateTime())
        .build();

    list.add(t1);

    return list;
  }

  public List<TransactionEntity> getTransactionEntities(Instruction instruction) {
    List<TransactionEntity> ts = new ArrayList<>();
    instruction.getTransactions().forEach(t -> {
      TransactionEntity te = TransactionEntity.builder()
          .transactionRefId(t.getTransactionRefId())
          .acceptanceDateTime(t.getAcceptanceDateTime().toLocalDateTime())
          .transactionFlow(t.getTransactionFlow())
          .paymentCategory(t.getPaymentCategory())
          .monetaryUnit(MonetaryUnit.valueOf(t.getMonetaryUnit()))
          .amount(t.getAmount())
          .build();
      ts.add(te);
    });
    return ts;
  }

  @Test
  public void testInitiateReserveWithFinalizationDateTime() throws Exception {
    // Arrange
    Account account = TestUtil.createAccount(accountRefId, profileRefId);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    when(validationService.isProfileAvailable(any(), any())).thenReturn(true);
    when(validationService.validateProfileAndAccount(any(), any(), any())).thenReturn(true);

    Instruction instruction = createInstruction();

    // Act
    Instruction savedInstruction = transactionService.initiateReserve(instruction, profileRefId, accountRefId, interactionId);

    // Assert
    assertNotNull(savedInstruction);
    assertNotNull(savedInstruction.getTransactions());

    // Verify finalizationDateTime is set for all transactions
    for (Transaction transaction : savedInstruction.getTransactions()) {
      assertNotNull(transaction.getFinalizationDateTime(),
          "FinalizationDateTime should be set for transaction: " + transaction.getTransactionRefId());

      // Verify it's set to a recent time (within last minute)
      assertTrue(transaction.getFinalizationDateTime().isAfter(OffsetDateTime.now().minusMinutes(1)),
          "FinalizationDateTime should be recent");

      // Verify all datetime fields are set
      assertNotNull(transaction.getAcceptanceDateTime());
      assertNotNull(transaction.getEffectiveDateTime());
      assertEquals(TransactionHoldType.INSTANT, transaction.getTransactionHold());
    }
  }

  @Test
  public void testFinalizationDateTimePersistence() throws Exception {
    // Arrange
    Account account = TestUtil.createAccount(accountRefId, profileRefId);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    when(validationService.isProfileAvailable(any(), any())).thenReturn(true);
    when(validationService.validateProfileAndAccount(any(), any(), any())).thenReturn(true);

    Instruction instruction = createInstruction();
    OffsetDateTime beforeSave = OffsetDateTime.now();

    // Act
    Instruction savedInstruction = transactionService.initiateReserve(instruction, profileRefId, accountRefId, interactionId);

    // Retrieve from database to verify persistence
    InstructionEntity savedEntity = instructionRepository.findByInstructionRefId(savedInstruction.getInstructionRefId());

    // Assert
    assertNotNull(savedEntity);
    assertNotNull(savedEntity.getTransactions());

    for (TransactionEntity transactionEntity : savedEntity.getTransactions()) {
      assertNotNull(transactionEntity.getFinalizationDateTime(),
          "FinalizationDateTime should be persisted in database");

      // Verify the time is between before save and now
      LocalDateTime finalizationTime = transactionEntity.getFinalizationDateTime();
      assertTrue(finalizationTime.isAfter(beforeSave.toLocalDateTime()) ||
              finalizationTime.isEqual(beforeSave.toLocalDateTime()),
          "FinalizationDateTime should be set after or at save time");
    }
  }
}