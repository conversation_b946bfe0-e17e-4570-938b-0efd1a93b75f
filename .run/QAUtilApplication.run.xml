<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="QAUtilApplication" type="Application" factoryName="Application" nameIsGenerated="true">
    <option name="ALTERNATIVE_JRE_PATH" value="17" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
    <option name="MAIN_CLASS_NAME" value="com.peoplestrust.qa.transaction.util.api.v1.QAUtilApplication" />
    <module name="tol-qa-util-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8086" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.peoplestrust.qa.transaction.util.api.v1.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>