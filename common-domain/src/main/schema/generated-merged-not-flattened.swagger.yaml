openapi: 3.0.3
info:
  title: Transaction Orchestration Layer
  description: >
    Transaction Orchestration Layer is the record-keeping system for a partners
    balances across the various Peoples Group products, with debit and credit
    account records validated by a trial balance.
  version: 1.4.0
servers:
  - url: 'https://ledger-stg-api.peoplescloud.io'
    description: Transaction Orchestration Layer Staging Environment
  - url: 'https://ledger-api.peoplesgroup.com'
    description: Transaction Orchestration Layer Production Environment
security:
  - OAuth2:
      - all
paths:
  '/v1/ledger/account/external/{account_ref_id}/balance':
    get:
      tags:
        - Ledger Account
      parameters:
        - $ref: common/common.yaml#/components/parameters/InteractionId
        - $ref: common/common.yaml#/components/parameters/InteractionTimestamp
        - $ref: common/common.yaml#/components/parameters/ProfileRefIdHeader
        - $ref: common/common.yaml#/components/parameters/AccountRefIdPath
      operationId: retreiveAccountBalanceByClient
      responses:
        '200':
          description: Balance succesfully retrieved
          content:
            application/json:
              schema:
                $ref: >-
                  #/components/schemas/RetrieveLedgerAccountBalanceByClientResponse
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              $ref: common/common.yaml#/components/headers/InteractionId
          content:
            application/json:
              schema:
                $ref: common/common.yaml#/components/schemas/ErrorResponse
        '404':
          description: Profile or account not found
          headers:
            x-pg-interaction-id:
              $ref: common/common.yaml#/components/headers/InteractionId
          content:
            application/json:
              schema:
                $ref: common/common.yaml#/components/schemas/ErrorResponse
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              $ref: common/common.yaml#/components/headers/InteractionId
          content:
            application/json:
              schema:
                $ref: common/common.yaml#/components/schemas/ErrorResponse
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: common/common.yaml#/components/headers/InteractionId
          content:
            application/json:
              schema:
                $ref: common/common.yaml#/components/schemas/ErrorResponse
components:
  schemas:
    RetrieveLedgerAccountBalanceByClientResponse:
      type: object
      properties:
        available_balance:
          $ref: common/common.yaml#/components/schemas/AvailableBalance
        account_balance:
          $ref: common/common.yaml#/components/schemas/AccountBalance
        fund_hold_amount:
          $ref: common/common.yaml#/components/schemas/FundHoldAmount
        prefund_reserve_amount:
          $ref: common/common.yaml#/components/schemas/PrefundReserveAmountTotal
        overdraft_amount:
          $ref: common/common.yaml#/components/schemas/OverdraftAmount
        effective_on:
          $ref: common/common.yaml#/components/schemas/EffectiveDateTime
  securitySchemes:
    OAuth2:
      type: oauth2
      description: This API uses OAuth 2.0 with client credentials flow
      flows:
        clientCredentials:
          tokenUrl: /oauth2/token
          scopes:
            all: access to all operations
