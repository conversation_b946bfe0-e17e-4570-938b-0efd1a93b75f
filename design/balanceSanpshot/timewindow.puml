@startuml
!theme toy
skinparam backgroundColor #f8f9fa
skinparam defaultFontSize 12

title <color:darkblue><b>Refactored Balance Snapshot Generation Logic</b></color>

== Main Flow ==

participant "<color:purple><b>generateBalanceEntities()</b></color>" as Main #E6E6FA
participant "<color:blue><b>Process With Previous</b></color>" as WithPrev #lightblue
participant "<color:green><b>Process Without Previous</b></color>" as NoPrev #lightgreen
participant "<color:orange><b>fetchTransactionAmounts()</b></color>" as FetchAmounts #FFE4B5
participant "<color:coral><b>createBalanceEntity()</b></color>" as CreateEntity #FFB6C1

== Timeline References ==
box "<color:darkgray><b>Time Points</b></color>" #f5f5f5
    participant "<b>2 Weeks Ago</b>" as TwoWeeks #lightgray
    participant "<b>Previous Snapshot End</b>" as PrevEnd #lightyellow
    participant "<b>Now - 1 Hour</b>" as EffectiveTo #lightcyan
    participant "<b>Pending Window Start</b>" as PendingStart #wheat
end box

== Initialization ==
Main -> Main : Set up timezone (ET)
Main -> Main : Calculate effectiveFromTwoWeeksDateTime
Main -> Main : Calculate effectiveToDateTimeStartOfDay (now - 1hr)
Main -> Main : Retrieve all accounts

== Per Account Processing ==

group <color:darkblue><b>For Each Account</b></color>
    Main -> Main : Check for previous snapshot

    alt <color:blue><b>Previous Snapshot EXISTS</b></color>
        Main -> WithPrev : processAccountWithPreviousSnapshot()

        WithPrev -> WithPrev : Check if snapshot already exists\nfor current period

        alt <color:gray>Already exists</color>
            WithPrev --> Main : <color:gray>Skip (avoid duplicate)</color>
        else <color:blue>Create new snapshot</color>
            note over WithPrev, PrevEnd #lightblue
                <b>Date Range:</b>
                FROM: previousSnapshot.effectiveToDateTime
                TO: now - 1 hour
            end note

            PrevEnd -[#blue]-> EffectiveTo : <color:blue><b>Continuation Range</b></color>

            WithPrev -> FetchAmounts : Fetch amounts for range
            WithPrev -> CreateEntity : Create with cumulative totals\n(current + previous)
        end

    else <color:green><b>No Previous Snapshot</b></color>
        Main -> NoPrev : processAccountWithoutPreviousSnapshot()

        note over NoPrev, TwoWeeks #lightgreen
            <b>Date Range:</b>
            FROM: 2 weeks ago
            TO: now - 1 hour
        end note

        TwoWeeks -[#green]-> EffectiveTo : <color:green><b>Initial Range</b></color>

        NoPrev -> FetchAmounts : Fetch amounts for range
        NoPrev -> CreateEntity : Create with new totals only
    end
end

== Common Transaction Fetching ==

group <color:orange><b>fetchTransactionAmounts()</b></color>
    FetchAmounts -> FetchAmounts : sumAmountByDateRangeBS()\n<i>Total transaction amount</i>
    FetchAmounts -> FetchAmounts : getReserveAmountBS()\n<i>Reserve amount</i>
    FetchAmounts -> FetchAmounts : getFundHoldAmount()\n<i>Fund hold amount</i>

    == Pending Window Calculation ==
    FetchAmounts -> PendingStart : Calculate: effectiveTo - (expirationDays + 1)
    PendingStart -[#orange]-> EffectiveTo : <color:orange><b>Pending Window</b></color>
    FetchAmounts -> FetchAmounts : getPendingAmountBS()\n<i>Pending transactions in window</i>

    FetchAmounts --> FetchAmounts : Return TransactionAmounts object
    note right FetchAmounts #FFE4B5
        <b>TransactionAmounts:</b>
        • totalAmount
        • reserveTotal
        • fundHoldAmount
        • pendingTotal
    end note
end

== Balance Entity Creation ==

CreateEntity -> CreateEntity : Set monetary unit
CreateEntity -> CreateEntity : Set profile & account IDs
CreateEntity -> CreateEntity : Set all amount fields
CreateEntity -> CreateEntity : Set effective date range
CreateEntity --> Main : Return BalanceEntity

Main -> Main : Save to repository
Main -> Main : Log completion

== Key Improvements ==

note bottom #lightyellow
    <color:darkblue><b>Refactoring Benefits:</b></color>

    <color:green>✓</color> <b>No Duplication:</b> Common logic extracted to reusable methods
    <color:green>✓</color> <b>Clear Separation:</b> Different scenarios handled by different methods
    <color:green>✓</color> <b>Data Grouping:</b> TransactionAmounts bundles related data
    <color:green>✓</color> <b>Single Responsibility:</b> Each method has one clear purpose
    <color:green>✓</color> <b>Testability:</b> Smaller, focused methods easier to test
    <color:green>✓</color> <b>Maintainability:</b> Changes isolated to specific methods
end note

@enduml