@startuml
!theme plain
left to right direction

skinparam backgroundColor white
skinparam shadowing false
skinparam defaultFontSize 14
skinparam state {
  ArrowColor #666
  ArrowThickness 2
  BorderThickness 2
  RoundCorner 20
  FontSize 14
}
skinparam note {
  FontSize 12
  RoundCorner 15
  BorderColor #A0A0A0
}

' === Title ===
title <size:22><b>💳 Real-Time Balance Calculation Timeline</b></size>\n<size:16><i>Visualizing the 32-Day Pending Window and Auto-Rollback</i></size>\n\n

' === Color & Style Definitions for each state ===
skinparam state<<past>> {
  BackgroundColor #F5F5F5
  BorderColor #BDBDBD
  FontColor #616161
}
skinparam state<<pending>> {
  BackgroundColor #FFF3E0
  BorderColor #FFB74D
  FontColor #E65100
}
skinparam state<<snapshot>> {
  BackgroundColor #E8F5E9
  BorderColor #81C784
  FontColor #1B5E20
}
skinparam state<<realtime>> {
  BackgroundColor #E3F2FD
  BorderColor #64B5F6
  FontColor #0D47A1
}
skinparam state<<final>> {
  BackgroundColor #F3E5F5
  BorderColor #BA68C8
  FontColor #4A148C
}

' === Timeline States (the main components) ===
state "<b><color:#616161>🕐 Past</b>\n> 32 days ago" as PAST <<past>>
state "<b><color:#E65100>⏳ Pending Window</b>\nLast 32 days" as PENDING <<pending>>
state "<b><color:#1B5E20>📸 Snapshot</b>\nLast Persisted State" as SNAPSHOT <<snapshot>>
state "<b><color:#0D47A1>⚡ Real-Time Delta</b>\nSince Snapshot" as RT <<realtime>>
state "<b><color:#4A148C>💰 Effective Balance</b>\nFinal Calculation" as FINAL <<final>>

' === Flow Arrows connecting the states ===
PAST -[#BDBDBD,bold]-> PENDING : <size:12>32-Day\nBoundary</size>
PENDING -[#FFB74D,bold]-> SNAPSHOT : <size:12>Snapshot\nCreation</size>
SNAPSHOT -[#64B5F6,bold]-> RT : <size:12>Live\nTransactions</size>
RT -[#BA68C8,bold]-> FINAL : <size:12>Final\nCalculation</size>

' === Auto-Rollback Feedback Loop Arrow (FIXED) ===
' The dots "." were replaced with dashes "-" for correct syntax
PENDING -[dotted,bold,#E53935]-> PAST : <size:12><color:#E53935><b>AUTO-ROLLBACK</b></color>\n<size:10><color:#E53935>if pending > 32d</color>

' === Descriptive Notes attached to states ===
note top of PAST #FAFAFA
  <b>🚫 Exclusion Zone</b>
  ────────────────
  • Transactions older than 32 days are ignored.
  • Includes `ROLLBACKED` / `ROLLBACKED_SYSTEM`.
  • <b>Result:</b> No impact on final balance calculations.
end note

note bottom of PENDING #FFF8F0
  <b>🔄 Dynamic Pending Window</b>
  ──────────────────────────
  <b>Start Date = MAX of:</b>
  1.  `Current Date - 32 days`
  2.  `Date of last Auto-Rollback`
  <b>Includes:</b> `INIT_PENDING` & `PENDING` transactions.
end note

note top of SNAPSHOT #F1F8E9
  <b>💾 Persisted State</b>
  ─────────────────
  <b>BalanceSnapshotService</b> stores pre-calculated totals:
  • `total_amount`
  • `total_reserve_amount`
  • `total_pending_amount`
  This provides a fast, stable baseline for calculations.
end note

note bottom of RT #EBF5FF
  <b>🔥 Live Calculation</b>
  ──────────────────
  A real-time sum of all credits and debits
  that have occurred since the last snapshot.
  <b>Query:</b> `sumAmountByDateRange()`
  <b>Range:</b> `snapshot.effectiveTo` → `now`
end note

note top of FINAL #F9F0FA
  <b>✅ Final Output for Validation</b>
  ─────────────────────────
  The value used for critical business logic:
  • Balance checks & transaction approvals
  • Overdraft and credit limit validation
end note

' === Legend at the bottom ===
legend bottom center
|= Color |= Segment |= Status in SUM |
|<#F5F5F5> Grey | Past (> 32 days) | ❌ **Excluded** |
|<#FFF3E0> Orange | Pending Window (≤ 32d) | ✅ **Included** |
|<#E8F5E9> Green | Snapshot (Baseline) | 💾 **Stored** |
|<#E3F2FD> Blue | Real-Time Delta | ✅ **Included** |
|<#F3E5F5> Purple | Effective Balance | 💰 **Result** |
endlegend

@enduml